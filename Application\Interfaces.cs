using Kufranjeh.PMS.Domain;

namespace Kufranjeh.PMS.Application;

public interface IAuthService
{
    Task<Employee?> AuthenticateAsync(string username, string password, CancellationToken ct = default);
}

public interface IProjectService
{
    Task<int> CreateProjectAsync(Project project, CancellationToken ct = default);
    Task UpdateProjectAsync(Project project, CancellationToken ct = default);
    Task DeleteProjectAsync(int projectId, CancellationToken ct = default);
    Task<Project?> GetProjectAsync(int projectId, CancellationToken ct = default);
    Task<IReadOnlyList<Project>> GetProjectsAsync(ProjectStatus? status = null, CancellationToken ct = default);
}

public interface ITaskService
{
    Task<int> CreateTaskAsync(TaskItem task, CancellationToken ct = default);
    Task UpdateTaskAsync(TaskItem task, CancellationToken ct = default);
    Task DeleteTaskAsync(int taskId, CancellationToken ct = default);
    Task<IReadOnlyList<TaskItem>> GetTasksByProjectAsync(int projectId, CancellationToken ct = default);
}

public interface IReportingService
{
    Task<string> GenerateReportAsync(string templatePath, DateTime from, DateTime to, string exportFormat = "PDF", CancellationToken ct = default);
}

public interface ITemplateService
{
    Task<IReadOnlyList<ReportTemplate>> GetActiveTemplatesAsync(string? templateType = null, CancellationToken ct = default);
}

