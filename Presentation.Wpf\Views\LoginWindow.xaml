<Window x:Class="Presentation.Wpf.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول" Height="250" Width="400" WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="اسم المستخدم"/>
        <TextBox x:Name="UsernameBox" Grid.Row="1" Margin="0,4,0,10"/>
        <TextBlock Grid.Row="2" Text="كلمة المرور"/>
        <PasswordBox x:Name="PasswordBox" Grid.Row="3" Margin="0,4,0,10"/>
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
            <Button x:Name="LoginBtn" Content="دخول" Width="100" Click="LoginBtn_Click"/>
            <TextBlock x:Name="StatusText" Margin="10,0,0,0" VerticalAlignment="Center"/>
        </StackPanel>
    </Grid>
</Window>

