﻿#pragma checksum "..\..\..\..\Views\DashboardWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E99DDA137C0FB27EDDFC5B3B5087B402FD290D81"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Presentation.Wpf.Views {
    
    
    /// <summary>
    /// DashboardWindow
    /// </summary>
    public partial class DashboardWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProjectsText;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveProjectsText;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedProjectsText;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalBudgetText;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshStats;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewProjects;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewTasks;
        
        #line default
        #line hidden
        
        
        #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActivityText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Presentation.Wpf;component/views/dashboardwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalProjectsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ActiveProjectsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CompletedProjectsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TotalBudgetText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BtnRefreshStats = ((System.Windows.Controls.Button)(target));
            
            #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
            this.BtnRefreshStats.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshStats_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnViewProjects = ((System.Windows.Controls.Button)(target));
            
            #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
            this.BtnViewProjects.Click += new System.Windows.RoutedEventHandler(this.BtnViewProjects_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnViewTasks = ((System.Windows.Controls.Button)(target));
            
            #line 1 "..\..\..\..\Views\DashboardWindow.xaml"
            this.BtnViewTasks.Click += new System.Windows.RoutedEventHandler(this.BtnViewTasks_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ActivityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

