# نظام إدارة مشاريع بلدية كفرنجة

نظام إدارة مشاريع شامل مطور خصيصاً لبلدية كفرنجة باستخدام تقنيات .NET الحديثة مع واجهة عربية كاملة.

## المميزات الرئيسية

### ✅ المميزات المكتملة
- **إدارة المشاريع**: عرض وإدارة المشاريع مع تفاصيل شاملة
- **إدارة المهام**: تتبع المهام المرتبطة بالمشاريع
- **لوحة التحكم**: عرض إحصائيات شاملة للمشاريع والميزانيات
- **نظام المصادقة**: تسجيل دخول آمن للمستخدمين
- **قاعدة بيانات Access**: تخزين محلي آمن للبيانات
- **واجهة عربية**: دعم كامل للغة العربية مع اتجاه النص من اليمين لليسار

### 🚧 قيد التطوير
- **نظام التقارير**: تقارير RDLC مع تصدير PDF/Excel/Word
- **إدارة المستخدمين**: إدارة الأدوار والصلاحيات
- **نماذج ذكية**: نماذج متقدمة لإدخال البيانات

## التقنيات المستخدمة

- **Frontend**: WPF (Windows Presentation Foundation)
- **Backend**: .NET 8.0 / .NET 10.0
- **قاعدة البيانات**: Microsoft Access (.accdb)
- **معمارية**: Clean Architecture مع فصل الطبقات
- **اللغة**: C# مع دعم العربية

## هيكل المشروع

```
Kufranjeh.PMS/
├── Domain/                     # طبقة النطاق (Entities, Enums)
├── Application/               # طبقة التطبيق (Interfaces, Services)
├── Infrastructure.Access/     # طبقة الوصول للبيانات (Access DB)
├── Infrastructure.Reporting/  # طبقة التقارير
├── Presentation.Wpf/         # طبقة العرض (WPF UI)
└── db/                       # قاعدة البيانات
```

## متطلبات التشغيل

### متطلبات النظام
- Windows 10/11
- .NET 8.0 Runtime أو أحدث
- Microsoft Access Database Engine (ACE)

### قاعدة البيانات
- ملف قاعدة البيانات: `db/Kufranjah.accdb`
- يتم إنشاء الجداول تلقائياً عند التشغيل الأول
- بيانات أولية تتضمن:
  - مستخدم النظام: `sysmgr` / `Admin@123`
  - قسم الإدارة الهندسية
  - قالب تقرير افتراضي

## التشغيل

### من سطر الأوامر
```bash
# استنساخ المشروع
git clone [repository-url]
cd kofranjeh

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run --project Presentation.Wpf
```

### من Visual Studio
1. افتح ملف `Kufranjeh.PMS.sln`
2. اضبط `Presentation.Wpf` كمشروع البداية
3. اضغط F5 للتشغيل

## استخدام النظام

### تسجيل الدخول
- اسم المستخدم: `sysmgr`
- كلمة المرور: `Admin@123`

### الواجهات الرئيسية
1. **الواجهة الرئيسية**: نقطة الانطلاق لجميع الوظائف
2. **إدارة المشاريع**: عرض وإدارة المشاريع
3. **إدارة المهام**: تتبع مهام المشاريع
4. **لوحة التحكم**: إحصائيات ومؤشرات الأداء

## قاعدة البيانات

### الجداول الرئيسية
- `Projects`: بيانات المشاريع
- `Tasks`: مهام المشاريع
- `Employees`: بيانات الموظفين
- `Departments`: الأقسام
- `Reports`: سجل التقارير
- `ReportTemplates`: قوالب التقارير
- `Attachments`: المرفقات
- `AuditLogs`: سجل العمليات
- `UserRoles`: أدوار المستخدمين

### العلاقات
- مشروع واحد يحتوي على عدة مهام
- موظف واحد يمكن أن يُكلف بعدة مهام
- كل مشروع ينتمي لقسم محدد

## الأمان

- تشفير كلمات المرور باستخدام Salt + Hash
- فصل طبقات الوصول للبيانات
- التحقق من صحة البيانات المدخلة
- سجل مراجعة للعمليات الحساسة

## التطوير المستقبلي

### المرحلة التالية
- [ ] نظام التقارير المتقدم
- [ ] إدارة المستخدمين والأدوار
- [ ] نماذج ذكية للبيانات
- [ ] تحسينات الواجهة
- [ ] نظام الإشعارات
- [ ] تصدير البيانات

### تحسينات مقترحة
- دعم قواعد بيانات أخرى (SQL Server)
- واجهة ويب
- تطبيق موبايل
- تكامل مع أنظمة أخرى

## المساهمة

للمساهمة في تطوير النظام:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## الترخيص

هذا المشروع مطور خصيصاً لبلدية كفرنجة.

## الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة**: فريق تطوير أنظمة البلدية  
**تاريخ آخر تحديث**: أغسطس 2025  
**الإصدار**: 1.0.0
