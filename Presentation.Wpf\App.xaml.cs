using System.Configuration;
using System.Data;
using System.Windows;

namespace Presentation.Wpf;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new Kufranjeh.PMS.Infrastructure.Access.AccessConnectionFactory(connStr);
        var init = new Kufranjeh.PMS.Infrastructure.Access.DatabaseInitializer(factory);
        _ = init.EnsureCreatedAsync();
        var seeder = new Kufranjeh.PMS.Infrastructure.Access.Seed.DbSeeder(factory);
        _ = seeder.SeedAsync();

        // Show Login, then MainWindow
        var login = new Presentation.Wpf.Views.LoginWindow();
        if (login.ShowDialog() == true)
        {
            var main = new MainWindow();
            main.Show();
        }
        else
        {
            Shutdown();
        }
    }
}


