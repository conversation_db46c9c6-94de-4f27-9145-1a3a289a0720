using System.Data;
using System.Data.OleDb;

namespace Kufranjeh.PMS.Infrastructure.Access;

public sealed class DatabaseInitializer
{
    private readonly AccessConnectionFactory _factory;
    public DatabaseInitializer(AccessConnectionFactory factory) => _factory = factory;

    public async Task EnsureCreatedAsync(CancellationToken ct = default)
    {
        if (!File.Exists(GetDbPath()))
        {
            // Cannot create .accdb programmatically without COM/ADOX; require the file to exist
            // Provide guidance to user in README; here we bail out gracefully.
            return;
        }

        using var con = _factory.Create();
        await con.OpenAsync(ct);

        // Create tables if missing
        await EnsureTableAsync(con, "Departments", GetCreateDepartments());
        await EnsureTableAsync(con, "Employees", GetCreateEmployees());
        await EnsureTableAsync(con, "Projects", GetCreateProjects());
        await EnsureTableAsync(con, "Tasks", GetCreateTasks());
        await EnsureTableAsync(con, "Reports", GetCreateReports());
        await EnsureTableAsync(con, "ReportTemplates", GetCreateReportTemplates());
        await EnsureTableAsync(con, "Attachments", GetCreateAttachments());
        await EnsureTableAsync(con, "AuditLogs", GetCreateAuditLogs());
        await EnsureTableAsync(con, "UserRoles", GetCreateUserRoles());

        // Indexes (idempotent via try/catch)
        await TryExecuteAsync(con, "CREATE UNIQUE INDEX ux_Employees_Username ON Employees(Username)", ct);
        await TryExecuteAsync(con, "CREATE INDEX ix_Employees_DepartmentID ON Employees(DepartmentID)", ct);
        await TryExecuteAsync(con, "CREATE INDEX ix_Tasks_ProjectID ON Tasks(ProjectID)", ct);
        await TryExecuteAsync(con, "CREATE INDEX ix_Tasks_AssignedTo ON Tasks(AssignedTo)", ct);
        await TryExecuteAsync(con, "CREATE INDEX ix_Reports_GeneratedBy ON Reports(GeneratedBy)", ct);

        // Foreign keys (may fail if already exist)
        await TryExecuteAsync(con, "ALTER TABLE Employees ADD CONSTRAINT fk_Employees_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)", ct);
        await TryExecuteAsync(con, "ALTER TABLE Tasks ADD CONSTRAINT fk_Tasks_Projects FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID)", ct);
        await TryExecuteAsync(con, "ALTER TABLE Tasks ADD CONSTRAINT fk_Tasks_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Employees(EmployeeID)", ct);
        await TryExecuteAsync(con, "ALTER TABLE Reports ADD CONSTRAINT fk_Reports_GeneratedBy FOREIGN KEY (GeneratedBy) REFERENCES Employees(EmployeeID)", ct);
        await TryExecuteAsync(con, "ALTER TABLE Attachments ADD CONSTRAINT fk_Attachments_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Employees(EmployeeID)", ct);
        await TryExecuteAsync(con, "ALTER TABLE UserRoles ADD CONSTRAINT fk_UserRoles_Employees FOREIGN KEY (UserID) REFERENCES Employees(EmployeeID)", ct);
    }

    private static string GetDbPath()
    {
        // Relative path used in app: .\\db\\Kufranjah.accdb
        var path = Path.Combine(AppContext.BaseDirectory, "db", "Kufranjah.accdb");
        return path;
    }

    private static async Task EnsureTableAsync(OleDbConnection con, string tableName, string createSql)
    {
        if (!await TableExistsAsync(con, tableName))
        {
            using var cmd = new OleDbCommand(createSql, con);
            await cmd.ExecuteNonQueryAsync();
        }
    }

    private static async Task<bool> TableExistsAsync(OleDbConnection con, string tableName)
    {
        var restrictions = new string?[] { null, null, tableName, "TABLE" };
        var schema = con.GetSchema("Tables", restrictions);
        foreach (DataRow row in schema.Rows)
        {
            var name = row["TABLE_NAME"]?.ToString();
            if (string.Equals(name, tableName, StringComparison.OrdinalIgnoreCase))
                return true;
        }
        return false;
    }

    private static async Task TryExecuteAsync(OleDbConnection con, string sql, CancellationToken ct)
    {
        try
        {
            using var cmd = new OleDbCommand(sql, con);
            await cmd.ExecuteNonQueryAsync(ct);
        }
        catch (OleDbException)
        {
            // assume already exists
        }
    }

    private static string GetCreateDepartments() => @"CREATE TABLE Departments (
        DepartmentID AUTOINCREMENT PRIMARY KEY,
        DepartmentName TEXT(255) NOT NULL
    )";

    private static string GetCreateEmployees() => @"CREATE TABLE Employees (
        EmployeeID AUTOINCREMENT PRIMARY KEY,
        FullName TEXT(255) NOT NULL,
        DepartmentID LONG,
        Position TEXT(100),
        ContactInfo TEXT(255),
        Username TEXT(100) NOT NULL,
        PasswordHash TEXT(255) NOT NULL,
        PasswordSalt TEXT(255) NOT NULL,
        IsActive YESNO NOT NULL
    )";

    private static string GetCreateProjects() => @"CREATE TABLE Projects (
        ProjectID AUTOINCREMENT PRIMARY KEY,
        ProjectName TEXT(255) NOT NULL,
        Location TEXT(255),
        StartDate DATETIME NOT NULL,
        EndDate DATETIME,
        Budget CURRENCY NOT NULL,
        Status TEXT(50) NOT NULL,
        ContractorID LONG,
        ProgressPercent INTEGER NOT NULL,
        RiskLevel TEXT(50)
    )";

    private static string GetCreateTasks() => @"CREATE TABLE Tasks (
        TaskID AUTOINCREMENT PRIMARY KEY,
        ProjectID LONG NOT NULL,
        TaskName TEXT(255) NOT NULL,
        AssignedTo LONG,
        StartDate DATETIME NOT NULL,
        EndDate DATETIME,
        Status TEXT(50) NOT NULL,
        ProgressPercent INTEGER NOT NULL,
        Description LONGTEXT
    )";

    private static string GetCreateReports() => @"CREATE TABLE Reports (
        ReportID AUTOINCREMENT PRIMARY KEY,
        ReportType TEXT(50) NOT NULL,
        GeneratedBy LONG NOT NULL,
        GenerationDate DATETIME NOT NULL,
        PeriodStart DATETIME,
        PeriodEnd DATETIME,
        OutputPath TEXT(255)
    )";

    private static string GetCreateReportTemplates() => @"CREATE TABLE ReportTemplates (
        TemplateID AUTOINCREMENT PRIMARY KEY,
        TemplateName TEXT(255) NOT NULL,
        TemplateType TEXT(50),
        FilePath TEXT(255) NOT NULL,
        Version TEXT(50),
        IsActive YESNO NOT NULL
    )";

    private static string GetCreateAttachments() => @"CREATE TABLE Attachments (
        AttachmentID AUTOINCREMENT PRIMARY KEY,
        EntityType TEXT(50) NOT NULL,
        EntityID LONG NOT NULL,
        FilePath TEXT(255) NOT NULL,
        Title TEXT(255),
        UploadedBy LONG NOT NULL,
        UploadedAt DATETIME NOT NULL
    )";

    private static string GetCreateAuditLogs() => @"CREATE TABLE AuditLogs (
        LogID AUTOINCREMENT PRIMARY KEY,
        UserID LONG NOT NULL,
        Action TEXT(100) NOT NULL,
        EntityType TEXT(50),
        EntityID LONG,
        Timestamp DATETIME NOT NULL
    )";

    private static string GetCreateUserRoles() => @"CREATE TABLE UserRoles (
        UserID LONG NOT NULL,
        Role TEXT(50) NOT NULL,
        CONSTRAINT pk_UserRoles PRIMARY KEY (UserID, Role)
    )";
}

