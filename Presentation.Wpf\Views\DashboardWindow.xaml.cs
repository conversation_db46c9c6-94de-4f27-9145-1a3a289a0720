﻿using System.Windows;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Domain;
using Kufranjeh.PMS.Infrastructure.Access;

namespace Presentation.Wpf.Views;

public partial class DashboardWindow : Window
{
    private readonly IProjectService _projectService;

    public DashboardWindow()
    {
        InitializeComponent();
        
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        _projectService = new ProjectService(factory);
        
        Loaded += DashboardWindow_Loaded;
    }

    private async void DashboardWindow_Loaded(object sender, RoutedEventArgs e)
    {
        await LoadStatisticsAsync();
    }

    private async Task LoadStatisticsAsync()
    {
        try
        {
            var allProjects = await _projectService.GetProjectsAsync();
            
            TotalProjectsText.Text = allProjects.Count.ToString();
            
            var activeProjects = allProjects.Where(p => p.Status == ProjectStatus.InProgress).Count();
            ActiveProjectsText.Text = activeProjects.ToString();
            
            var completedProjects = allProjects.Where(p => p.Status == ProjectStatus.Completed).Count();
            CompletedProjectsText.Text = completedProjects.ToString();
            
            var totalBudget = allProjects.Sum(p => p.Budget);
            TotalBudgetText.Text = totalBudget.ToString("C");
            
            ActivityText.Text = $"آخر تحديث: {DateTime.Now:dd/MM/yyyy HH:mm}\n" +
                               $"إجمالي المشاريع: {allProjects.Count}\n" +
                               $"المشاريع النشطة: {activeProjects}\n" +
                               $"المشاريع المكتملة: {completedProjects}";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل الإحصائيات:\n{ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnRefreshStats_Click(object sender, RoutedEventArgs e)
    {
        await LoadStatisticsAsync();
    }

    private void BtnViewProjects_Click(object sender, RoutedEventArgs e)
    {
        var projectsWindow = new ProjectsWindow();
        projectsWindow.Show();
    }

    private void BtnViewTasks_Click(object sender, RoutedEventArgs e)
    {
        var tasksWindow = new TasksWindow();
        tasksWindow.Show();
    }
}