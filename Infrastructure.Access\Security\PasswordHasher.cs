using System.Security.Cryptography;
using System.Text;

namespace Kufranjeh.PMS.Infrastructure.Access.Security;

public static class PasswordHasher
{
    public const int SaltSize = 16; // 128-bit
    public const int KeySize = 32;  // 256-bit
    public const int Iterations = 100_000;

    public static (string HashB64, string SaltB64) HashPassword(string password)
    {
        var salt = RandomNumberGenerator.GetBytes(SaltSize);
        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
        var key = pbkdf2.GetBytes(KeySize);
        return (Convert.ToBase64String(key), Convert.ToBase64String(salt));
    }

    public static bool Verify(string password, string hashB64, string saltB64)
    {
        var salt = Convert.FromBase64String(saltB64);
        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
        var key = pbkdf2.GetBytes(KeySize);
        var hash = Convert.FromBase64String(hashB64);
        return CryptographicOperations.FixedTimeEquals(key, hash);
    }
}

