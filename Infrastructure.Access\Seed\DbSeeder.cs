using System.Data.OleDb;
using Kufranjeh.PMS.Infrastructure.Access.Security;

namespace Kufranjeh.PMS.Infrastructure.Access.Seed;

public sealed class DbSeeder
{
    private readonly AccessConnectionFactory _factory;
    public DbSeeder(AccessConnectionFactory factory) => _factory = factory;

    public async Task SeedAsync(CancellationToken ct = default)
    {
        if (!File.Exists(Path.Combine(AppContext.BaseDirectory, "db", "Kufranjah.accdb"))) return;

        using var con = _factory.Create();
        await con.OpenAsync(ct);

        // Ensure Department exists
        var deptId = await EnsureDepartmentAsync(con, "الإدارة الهندسية", ct);

        // Ensure System Manager user exists
        await EnsureSystemManagerAsync(con, deptId, username: "sysmgr", password: "Admin@123", ct);

        // Ensure a sample report template record
        await EnsureReportTemplateAsync(con, "تقرير تقدم مشروع (افتراضي)", "Progress", @".\\reports\\templates\\ProjectProgress.rdlc", ct);
    }

    private static async Task<int> EnsureDepartmentAsync(OleDbConnection con, string name, CancellationToken ct)
    {
        using (var check = new OleDbCommand("SELECT DepartmentID FROM Departments WHERE DepartmentName=?", con))
        {
            check.Parameters.AddWithValue("@p1", name);
            var found = await check.ExecuteScalarAsync(ct);
            if (found is int id) return id;
            // Access returns double sometimes for AUTOINCREMENT identity scalar fetch; we'll re-select
        }
        using (var ins = new OleDbCommand("INSERT INTO Departments (DepartmentName) VALUES (?)", con))
        {
            ins.Parameters.AddWithValue("@p1", name);
            await ins.ExecuteNonQueryAsync(ct);
        }
        using (var re = new OleDbCommand("SELECT DepartmentID FROM Departments WHERE DepartmentName=?", con))
        {
            re.Parameters.AddWithValue("@p1", name);
            var idObj = await re.ExecuteScalarAsync(ct);
            return Convert.ToInt32(idObj);
        }
    }

    private static async Task EnsureSystemManagerAsync(OleDbConnection con, int departmentId, string username, string password, CancellationToken ct)
    {
        using (var check = new OleDbCommand("SELECT EmployeeID FROM Employees WHERE Username=?", con))
        {
            check.Parameters.AddWithValue("@p1", username);
            var exists = await check.ExecuteScalarAsync(ct);
            if (exists != null && exists != DBNull.Value) return;
        }
        var (hash, salt) = PasswordHasher.HashPassword(password);
        using (var ins = new OleDbCommand(@"INSERT INTO Employees (FullName, DepartmentID, Position, ContactInfo, Username, PasswordHash, PasswordSalt, IsActive)
VALUES (?,?,?,?,?,?,?,?)", con))
        {
            ins.Parameters.AddWithValue("@p1", "مدير النظام");
            ins.Parameters.AddWithValue("@p2", departmentId);
            ins.Parameters.AddWithValue("@p3", "System Manager");
            ins.Parameters.AddWithValue("@p4", DBNull.Value);
            ins.Parameters.AddWithValue("@p5", username);
            ins.Parameters.AddWithValue("@p6", hash);
            ins.Parameters.AddWithValue("@p7", salt);
            ins.Parameters.AddWithValue("@p8", true);
            await ins.ExecuteNonQueryAsync(ct);
        }
        int userId;
        using (var sel = new OleDbCommand("SELECT EmployeeID FROM Employees WHERE Username=?", con))
        {
            sel.Parameters.AddWithValue("@p1", username);
            userId = Convert.ToInt32(await sel.ExecuteScalarAsync(ct));
        }
        using (var role = new OleDbCommand("INSERT INTO UserRoles (UserID, Role) VALUES (?,?)", con))
        {
            role.Parameters.AddWithValue("@p1", userId);
            role.Parameters.AddWithValue("@p2", "SystemManager");
            await role.ExecuteNonQueryAsync(ct);
        }
    }

    private static async Task EnsureReportTemplateAsync(OleDbConnection con, string name, string type, string filePath, CancellationToken ct)
    {
        using var check = new OleDbCommand("SELECT TemplateID FROM ReportTemplates WHERE TemplateName=?", con);
        check.Parameters.AddWithValue("@p1", name);
        var exists = await check.ExecuteScalarAsync(ct);
        if (exists != null && exists != DBNull.Value) return;

        using var ins = new OleDbCommand("INSERT INTO ReportTemplates (TemplateName, TemplateType, FilePath, Version, IsActive) VALUES (?,?,?,?,?)", con);
        ins.Parameters.AddWithValue("@p1", name);
        ins.Parameters.AddWithValue("@p2", type);
        ins.Parameters.AddWithValue("@p3", filePath);
        ins.Parameters.AddWithValue("@p4", "1.0");
        ins.Parameters.AddWithValue("@p5", true);
        await ins.ExecuteNonQueryAsync(ct);
    }
}

