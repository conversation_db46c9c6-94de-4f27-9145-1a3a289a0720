﻿using System.Text;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Infrastructure.Access;
using Kufranjeh.PMS.Infrastructure.Reporting;

using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace Presentation.Wpf;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly IProjectService _projects;
    private readonly IReportingService _reporting;

    public MainWindow()
    {
        InitializeComponent();

        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        _projects = new ProjectService(factory);
        _reporting = new ReportingService();
    }

    private void BtnProjects_Click(object sender, RoutedEventArgs e)
    {
        var projectsWindow = new Views.ProjectsWindow();
        projectsWindow.Show();
    }

    private void BtnTasks_Click(object sender, RoutedEventArgs e)
    {
        var tasksWindow = new Views.TasksWindow();
        tasksWindow.Show();
    }

    private void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("نظام التقارير قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        var dashboardWindow = new Views.DashboardWindow();
        dashboardWindow.Show();
    }
}