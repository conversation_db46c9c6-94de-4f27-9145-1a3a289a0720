using System.Configuration;
using System.Data;
using System.Windows;
using Kufranjeh.PMS.Infrastructure.Access;
using Kufranjeh.PMS.Infrastructure.Access.Seed;
using Presentation.Wpf.Views;

namespace Presentation.Wpf;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        var init = new DatabaseInitializer(factory);
        _ = init.EnsureCreatedAsync();
        var seeder = new DbSeeder(factory);
        _ = seeder.SeedAsync();

        // Show Login, then MainWindow
        var login = new LoginWindow();
        if (login.ShowDialog() == true)
        {
            var main = new MainWindow();
            main.Show();
        }
        else
        {
            Shutdown();
        }
    }
}


