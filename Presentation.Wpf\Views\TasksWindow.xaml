﻿<Window x:Class="Presentation.Wpf.Views.TasksWindow" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Title="إدارة المهام" Height="500" Width="800" FlowDirection="RightToLeft"><Grid Margin="10"><Grid.RowDefinitions><RowDefinition Height="Auto"/><RowDefinition Height="Auto"/><RowDefinition Height="*"/><RowDefinition Height="Auto"/></Grid.RowDefinitions><TextBlock Grid.Row="0" Text="قائمة المهام" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" HorizontalAlignment="Center"/><StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10"><Button Name="BtnAddTask" Content="إضافة مهمة جديدة" Margin="5" Padding="10,5"/><Button Name="BtnRefresh" Content="تحديث" Margin="5" Padding="10,5"/></StackPanel><DataGrid Grid.Row="2" Name="TasksDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True" SelectionMode="Single"><DataGrid.Columns><DataGridTextColumn Header="رقم المهمة" Binding="{Binding TaskID}" Width="80"/><DataGridTextColumn Header="اسم المهمة" Binding="{Binding TaskName}" Width="200"/><DataGridTextColumn Header="المشروع" Binding="{Binding ProjectID}" Width="100"/><DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat=dd/MM/yyyy}" Width="100"/><DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/><DataGridTextColumn Header="نسبة الإنجاز" Binding="{Binding ProgressPercent}" Width="100"/></DataGrid.Columns></DataGrid><StatusBar Grid.Row="3"><StatusBarItem><TextBlock Name="StatusText" Text="جاهز"/></StatusBarItem></StatusBar></Grid></Window>