-- Reference schema (for documentation). The app creates tables if missing at runtime.

CREATE TABLE Departments (
    DepartmentID AUTOINCREMENT PRIMARY KEY,
    DepartmentName TEXT(255) NOT NULL
);

CREATE TABLE Employees (
    EmployeeID AUTOINCREMENT PRIMARY KEY,
    FullName TEXT(255) NOT NULL,
    DepartmentID LONG,
    Position TEXT(100),
    ContactInfo TEXT(255),
    Username TEXT(100) NOT NULL,
    PasswordHash TEXT(255) NOT NULL,
    PasswordSalt TEXT(255) NOT NULL,
    IsActive YESNO NOT NULL
);

CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(255) NOT NULL,
    Location TEXT(255),
    StartDate DATETIME NOT NULL,
    EndDate DATETIME,
    Budget CURRENCY NOT NULL,
    Status TEXT(50) NOT NULL,
    ContractorID LONG,
    ProgressPercent INTEGER NOT NULL,
    RiskLevel TEXT(50)
);

CREATE TABLE Tasks (
    TaskID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    TaskName TEXT(255) NOT NULL,
    AssignedTo LONG,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME,
    Status TEXT(50) NOT NULL,
    ProgressPercent INTEGER NOT NULL,
    Description LONGTEXT
);

CREATE TABLE Reports (
    ReportID AUTOINCREMENT PRIMARY KEY,
    ReportType TEXT(50) NOT NULL,
    GeneratedBy LONG NOT NULL,
    GenerationDate DATETIME NOT NULL,
    PeriodStart DATETIME,
    PeriodEnd DATETIME,
    OutputPath TEXT(255)
);

CREATE TABLE ReportTemplates (
    TemplateID AUTOINCREMENT PRIMARY KEY,
    TemplateName TEXT(255) NOT NULL,
    TemplateType TEXT(50),
    FilePath TEXT(255) NOT NULL,
    Version TEXT(50),
    IsActive YESNO NOT NULL
);

CREATE TABLE Attachments (
    AttachmentID AUTOINCREMENT PRIMARY KEY,
    EntityType TEXT(50) NOT NULL,
    EntityID LONG NOT NULL,
    FilePath TEXT(255) NOT NULL,
    Title TEXT(255),
    UploadedBy LONG NOT NULL,
    UploadedAt DATETIME NOT NULL
);

CREATE TABLE AuditLogs (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID LONG NOT NULL,
    Action TEXT(100) NOT NULL,
    EntityType TEXT(50),
    EntityID LONG,
    Timestamp DATETIME NOT NULL
);

CREATE TABLE UserRoles (
    UserID LONG NOT NULL,
    Role TEXT(50) NOT NULL,
    CONSTRAINT pk_UserRoles PRIMARY KEY (UserID, Role)
);

-- Indexes
CREATE UNIQUE INDEX ux_Employees_Username ON Employees(Username);
CREATE INDEX ix_Employees_DepartmentID ON Employees(DepartmentID);
CREATE INDEX ix_Tasks_ProjectID ON Tasks(ProjectID);
CREATE INDEX ix_Tasks_AssignedTo ON Tasks(AssignedTo);
CREATE INDEX ix_Reports_GeneratedBy ON Reports(GeneratedBy);

-- Foreign keys
ALTER TABLE Employees ADD CONSTRAINT fk_Employees_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);
ALTER TABLE Tasks ADD CONSTRAINT fk_Tasks_Projects FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID);
ALTER TABLE Tasks ADD CONSTRAINT fk_Tasks_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Employees(EmployeeID);
ALTER TABLE Reports ADD CONSTRAINT fk_Reports_GeneratedBy FOREIGN KEY (GeneratedBy) REFERENCES Employees(EmployeeID);
ALTER TABLE Attachments ADD CONSTRAINT fk_Attachments_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Employees(EmployeeID);
ALTER TABLE UserRoles ADD CONSTRAINT fk_UserRoles_Employees FOREIGN KEY (UserID) REFERENCES Employees(EmployeeID);

