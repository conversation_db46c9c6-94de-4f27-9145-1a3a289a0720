﻿using System.Collections.ObjectModel;
using System.Windows;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Domain;
using Kufranjeh.PMS.Infrastructure.Access;

namespace Presentation.Wpf.Views;

public partial class ProjectsWindow : Window
{
    private readonly IProjectService _projectService;
    private readonly ObservableCollection<ProjectViewModel> _projects;

    public ProjectsWindow()
    {
        InitializeComponent();
        
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        _projectService = new ProjectService(factory);
        
        _projects = new ObservableCollection<ProjectViewModel>();
        
        Loaded += ProjectsWindow_Loaded;
    }

    private async void ProjectsWindow_Loaded(object sender, RoutedEventArgs e)
    {
        await LoadProjectsAsync();
    }

    private async Task LoadProjectsAsync()
    {
        try
        {
            _projects.Clear();

            var projects = await _projectService.GetProjectsAsync();
            
            foreach (var project in projects)
            {
                _projects.Add(new ProjectViewModel(project));
            }

            MessageBox.Show($"تم تحميل {_projects.Count} مشروع", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل المشاريع:\n{ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

public class ProjectViewModel
{
    public ProjectViewModel(Project project)
    {
        ProjectID = project.ProjectID;
        ProjectName = project.ProjectName;
        Location = project.Location;
        StartDate = project.StartDate;
        EndDate = project.EndDate;
        Budget = project.Budget;
        Status = project.Status;
        ContractorID = project.ContractorID;
        ProgressPercent = project.ProgressPercent;
        RiskLevel = project.RiskLevel;
    }

    public int ProjectID { get; set; }
    public string ProjectName { get; set; }
    public string? Location { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public decimal Budget { get; set; }
    public ProjectStatus Status { get; set; }
    public int? ContractorID { get; set; }
    public int ProgressPercent { get; set; }
    public string? RiskLevel { get; set; }

    public string StatusText => Status switch
    {
        ProjectStatus.Planned => "مخطط",
        ProjectStatus.InProgress => "قيد التنفيذ",
        ProjectStatus.Completed => "مكتمل",
        ProjectStatus.OnHold => "معلق",
        ProjectStatus.Cancelled => "ملغي",
        _ => "غير محدد"
    };
}