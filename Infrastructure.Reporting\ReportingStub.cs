using Kufranjeh.PMS.Application;

namespace Kufranjeh.PMS.Infrastructure.Reporting;

public sealed class ReportingService : IReportingService
{
    public Task<string> GenerateReportAsync(string templatePath, DateTime from, DateTime to, string exportFormat = "PDF", CancellationToken ct = default)
    {
        // Stub: real implementation will use RDLC/ReportViewer.
        var output = Path.Combine(AppContext.BaseDirectory, "reports", "exports", $"Report_{DateTime.Now:yyyyMMdd_HHmmss}.{exportFormat.ToLowerInvariant()}");
        Directory.CreateDirectory(Path.GetDirectoryName(output)!);
        File.WriteAllText(output, $"Template={templatePath}; From={from:O}; To={to:O}; Format={exportFormat}");
        return Task.FromResult(output);
    }
}

