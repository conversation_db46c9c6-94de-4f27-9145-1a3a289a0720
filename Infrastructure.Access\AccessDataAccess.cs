using System.Data;
using System.Data.OleDb;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Domain;
using Kufranjeh.PMS.Infrastructure.Access.Security;

namespace Kufranjeh.PMS.Infrastructure.Access;

public sealed class AccessConnectionFactory
{
    private readonly string _connectionString;
    public AccessConnectionFactory(string connectionString)
    {
        _connectionString = connectionString;
    }
    public OleDbConnection Create() => new OleDbConnection(_connectionString);
}

public sealed class AuthService : IAuthService
{
    private readonly AccessConnectionFactory _factory;
    public AuthService(AccessConnectionFactory factory) => _factory = factory;

    public async Task<Employee?> AuthenticateAsync(string username, string password, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        using var cmd = new OleDbCommand("SELECT EmployeeID, FullName, Username, PasswordHash, PasswordSalt, IsActive FROM Employees WHERE Username=?", con);
        cmd.Parameters.AddWithValue("@p1", username);
        using var r = await cmd.ExecuteReaderAsync(ct);
        if (!await r.ReadAsync(ct)) return null;
        var isActive = r.GetBoolean(5);
        if (!isActive) return null;
        var hash = r.GetString(3);
        var salt = r.GetString(4);
        if (!PasswordHasher.Verify(password, hash, salt)) return null;
        return new Employee
        {
            EmployeeID = r.GetInt32(0),
            FullName = r.GetString(1),
            Username = r.GetString(2),
            PasswordHash = hash,
            PasswordSalt = salt,
            IsActive = isActive
        };
    }
}

public sealed class ProjectService : IProjectService
{
    private readonly AccessConnectionFactory _factory;
    public ProjectService(AccessConnectionFactory factory) => _factory = factory;

    public async Task<int> CreateProjectAsync(Project p, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("INSERT INTO Projects (ProjectName, Location, StartDate, EndDate, Budget, Status, ContractorID, ProgressPercent, RiskLevel) VALUES (?,?,?,?,?,?,?,?,?)", con);
        cmd.Parameters.AddWithValue("@p1", p.ProjectName);
        cmd.Parameters.AddWithValue("@p2", (object?)p.Location ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p3", p.StartDate);
        cmd.Parameters.AddWithValue("@p4", (object?)p.EndDate ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p5", p.Budget);
        cmd.Parameters.AddWithValue("@p6", p.Status.ToString());
        cmd.Parameters.AddWithValue("@p7", (object?)p.ContractorID ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p8", p.ProgressPercent);
        cmd.Parameters.AddWithValue("@p9", (object?)p.RiskLevel ?? DBNull.Value);
        await cmd.ExecuteNonQueryAsync(ct);
        cmd = new OleDbCommand("SELECT @@IDENTITY", con);
        var id = Convert.ToInt32(await cmd.ExecuteScalarAsync(ct));
        return id;
    }

    public async Task UpdateProjectAsync(Project p, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("UPDATE Projects SET ProjectName=?, Location=?, StartDate=?, EndDate=?, Budget=?, Status=?, ContractorID=?, ProgressPercent=?, RiskLevel=? WHERE ProjectID=?", con);
        cmd.Parameters.AddWithValue("@p1", p.ProjectName);
        cmd.Parameters.AddWithValue("@p2", (object?)p.Location ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p3", p.StartDate);
        cmd.Parameters.AddWithValue("@p4", (object?)p.EndDate ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p5", p.Budget);
        cmd.Parameters.AddWithValue("@p6", p.Status.ToString());
        cmd.Parameters.AddWithValue("@p7", (object?)p.ContractorID ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p8", p.ProgressPercent);
        cmd.Parameters.AddWithValue("@p9", (object?)p.RiskLevel ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p10", p.ProjectID);
        await cmd.ExecuteNonQueryAsync(ct);
    }

    public async Task DeleteProjectAsync(int projectId, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("DELETE FROM Projects WHERE ProjectID=?", con);
        cmd.Parameters.AddWithValue("@p1", projectId);
        await cmd.ExecuteNonQueryAsync(ct);
    }

    public async Task<Project?> GetProjectAsync(int projectId, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("SELECT ProjectID, ProjectName, Location, StartDate, EndDate, Budget, Status, ContractorID, ProgressPercent, RiskLevel FROM Projects WHERE ProjectID=?", con);
        cmd.Parameters.AddWithValue("@p1", projectId);
        using var r = await cmd.ExecuteReaderAsync(ct);
        if (!await r.ReadAsync(ct)) return null;
        return MapProject(r);
    }

    public async Task<IReadOnlyList<Project>> GetProjectsAsync(ProjectStatus? status = null, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        OleDbCommand cmd;
        if (status is null)
        {
            cmd = new OleDbCommand("SELECT ProjectID, ProjectName, Location, StartDate, EndDate, Budget, Status, ContractorID, ProgressPercent, RiskLevel FROM Projects", con);
        }
        else
        {
            cmd = new OleDbCommand("SELECT ProjectID, ProjectName, Location, StartDate, EndDate, Budget, Status, ContractorID, ProgressPercent, RiskLevel FROM Projects WHERE Status=?", con);
            cmd.Parameters.AddWithValue("@p1", status.Value.ToString());
        }
        var list = new List<Project>();
        using var r = await cmd.ExecuteReaderAsync(ct);
        while (await r.ReadAsync(ct)) list.Add(MapProject(r));
        return list;
    }

    private static Project MapProject(IDataRecord r)
    {
        return new Project
        {
            ProjectID = r.GetInt32(0),
            ProjectName = r.GetString(1),
            Location = r.IsDBNull(2) ? null : r.GetString(2),
            StartDate = r.GetDateTime(3),
            EndDate = r.IsDBNull(4) ? null : r.GetDateTime(4),
            Budget = r.GetDecimal(5),
            Status = Enum.TryParse<ProjectStatus>(r.GetString(6), out var st) ? st : ProjectStatus.Planned,
            ContractorID = r.IsDBNull(7) ? null : r.GetInt32(7),
            ProgressPercent = r.GetInt32(8),
            RiskLevel = r.IsDBNull(9) ? null : r.GetString(9)
        };
    }
}

public sealed class TaskService : ITaskService
{
    private readonly AccessConnectionFactory _factory;
    public TaskService(AccessConnectionFactory factory) => _factory = factory;

    public async Task<int> CreateTaskAsync(TaskItem task, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("INSERT INTO Tasks (ProjectID, TaskName, AssignedTo, StartDate, EndDate, Status, ProgressPercent, Description) VALUES (?,?,?,?,?,?,?,?)", con);
        cmd.Parameters.AddWithValue("@p1", task.ProjectID);
        cmd.Parameters.AddWithValue("@p2", task.TaskName);
        cmd.Parameters.AddWithValue("@p3", (object?)task.AssignedTo ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p4", task.StartDate);
        cmd.Parameters.AddWithValue("@p5", (object?)task.EndDate ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p6", task.Status.ToString());
        cmd.Parameters.AddWithValue("@p7", task.ProgressPercent);
        cmd.Parameters.AddWithValue("@p8", (object?)task.Description ?? DBNull.Value);
        await cmd.ExecuteNonQueryAsync(ct);
        cmd = new OleDbCommand("SELECT @@IDENTITY", con);
        var id = Convert.ToInt32(await cmd.ExecuteScalarAsync(ct));
        return id;
    }

    public async Task UpdateTaskAsync(TaskItem task, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("UPDATE Tasks SET ProjectID=?, TaskName=?, AssignedTo=?, StartDate=?, EndDate=?, Status=?, ProgressPercent=?, Description=? WHERE TaskID=?", con);
        cmd.Parameters.AddWithValue("@p1", task.ProjectID);
        cmd.Parameters.AddWithValue("@p2", task.TaskName);
        cmd.Parameters.AddWithValue("@p3", (object?)task.AssignedTo ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p4", task.StartDate);
        cmd.Parameters.AddWithValue("@p5", (object?)task.EndDate ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p6", task.Status.ToString());
        cmd.Parameters.AddWithValue("@p7", task.ProgressPercent);
        cmd.Parameters.AddWithValue("@p8", (object?)task.Description ?? DBNull.Value);
        cmd.Parameters.AddWithValue("@p9", task.TaskID);
        await cmd.ExecuteNonQueryAsync(ct);
    }

    public async Task DeleteTaskAsync(int taskId, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("DELETE FROM Tasks WHERE TaskID=?", con);
        cmd.Parameters.AddWithValue("@p1", taskId);
        await cmd.ExecuteNonQueryAsync(ct);
    }

    public async Task<IReadOnlyList<TaskItem>> GetTasksByProjectAsync(int projectId, CancellationToken ct = default)
    {
        using var con = _factory.Create();
        await con.OpenAsync();
        var cmd = new OleDbCommand("SELECT TaskID, ProjectID, TaskName, AssignedTo, StartDate, EndDate, Status, ProgressPercent, Description FROM Tasks WHERE ProjectID=?", con);
        cmd.Parameters.AddWithValue("@p1", projectId);
        var list = new List<TaskItem>();
        using var r = await cmd.ExecuteReaderAsync(ct);
        while (await r.ReadAsync(ct)) list.Add(MapTask(r));
        return list;
    }

    private static TaskItem MapTask(IDataRecord r)
    {
        return new TaskItem
        {
            TaskID = r.GetInt32(0),
            ProjectID = r.GetInt32(1),
            TaskName = r.GetString(2),
            AssignedTo = r.IsDBNull(3) ? null : r.GetInt32(3),
            StartDate = r.GetDateTime(4),
            EndDate = r.IsDBNull(5) ? null : r.GetDateTime(5),
            Status = Enum.TryParse<Domain.TaskStatus>(r.GetString(6), out var st) ? st : Domain.TaskStatus.NotStarted,
            ProgressPercent = r.GetInt32(7),
            Description = r.IsDBNull(8) ? null : r.GetString(8)
        };
    }
}

