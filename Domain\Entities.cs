namespace Kufranjeh.PMS.Domain;

public enum ProjectStatus
{
    Planned,
    InProgress,
    Completed,
    OnHold,
    Cancelled
}

public enum TaskStatus
{
    NotStarted,
    InProgress,
    Blocked,
    Done
}

public enum Role
{
    <PERSON><PERSON>,
    SystemManager,
    ProjectManager,
    Employee
}

public sealed class Project
{
    public int ProjectID { get; set; }
    public string ProjectName { get; set; } = string.Empty;
    public string? Location { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public decimal Budget { get; set; }
    public ProjectStatus Status { get; set; }
    public int? ContractorID { get; set; }
    public int ProgressPercent { get; set; }
    public string? RiskLevel { get; set; }
}

public sealed class TaskItem
{
    public int TaskID { get; set; }
    public int ProjectID { get; set; }
    public string TaskName { get; set; } = string.Empty;
    public int? AssignedTo { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TaskStatus Status { get; set; }
    public int ProgressPercent { get; set; }
    public string? Description { get; set; }
}

public sealed class Employee
{
    public int EmployeeID { get; set; }
    public string FullName { get; set; } = string.Empty;
    public int? DepartmentID { get; set; }
    public string? Position { get; set; }
    public string? ContactInfo { get; set; }
    public string Username { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string PasswordSalt { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

public sealed class Department
{
    public int DepartmentID { get; set; }
    public string DepartmentName { get; set; } = string.Empty;
}

public sealed class Attachment
{
    public int AttachmentID { get; set; }
    public string EntityType { get; set; } = string.Empty; // Project/Task/Report
    public int EntityID { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string? Title { get; set; }
    public int UploadedBy { get; set; }
    public DateTime UploadedAt { get; set; }
}

public sealed class ReportRecord
{
    public int ReportID { get; set; }
    public string ReportType { get; set; } = string.Empty; // Daily/Weekly/Monthly/Final
    public int GeneratedBy { get; set; }
    public DateTime GenerationDate { get; set; }
    public DateTime? PeriodStart { get; set; }
    public DateTime? PeriodEnd { get; set; }
    public string? OutputPath { get; set; }
}

public sealed class ReportTemplate
{
    public int TemplateID { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty; // Progress/Financial/Safety/Final
    public string FilePath { get; set; } = string.Empty; // path to RDLC or other template file
    public string? Version { get; set; }
    public bool IsActive { get; set; } = true;
}

public sealed class UserRole
{
    public int UserID { get; set; }
    public Role Role { get; set; }
}

