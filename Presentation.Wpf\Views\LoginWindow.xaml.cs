using System.Windows;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Infrastructure.Access;

namespace Presentation.Wpf.Views;

public partial class LoginWindow : Window
{
    private readonly IAuthService _auth;

    public LoginWindow()
    {
        InitializeComponent();
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        _auth = new AuthService(factory);
    }

    private async void LoginBtn_Click(object sender, RoutedEventArgs e)
    {
        LoginBtn.IsEnabled = false;
        StatusText.Text = "جاري التحقق...";
        try
        {
            var user = await _auth.AuthenticateAsync(UsernameBox.Text, PasswordBox.Password);
            if (user is null)
            {
                StatusText.Text = "بيانات غير صحيحة";
                return;
            }
            DialogResult = true;
            Close();
        }
        finally
        {
            LoginBtn.IsEnabled = true;
        }
    }
}

