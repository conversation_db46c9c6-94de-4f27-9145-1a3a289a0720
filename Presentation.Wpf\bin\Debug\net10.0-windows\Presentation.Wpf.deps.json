{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"Presentation.Wpf/1.0.0": {"dependencies": {"Application": "1.0.0", "Infrastructure.Access": "1.0.0", "Infrastructure.Reporting": "1.0.0"}, "runtime": {"Presentation.Wpf.dll": {}}}, "System.Data.OleDb/9.0.8": {"runtime": {"lib/net9.0/System.Data.OleDb.dll": {"assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}}, "Application/1.0.0": {"dependencies": {"Domain": "1.0.0"}, "runtime": {"Application.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Domain/1.0.0": {"runtime": {"Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Infrastructure.Access/1.0.0": {"dependencies": {"Application": "1.0.0", "Domain": "1.0.0", "System.Data.OleDb": "9.0.8"}, "runtime": {"Infrastructure.Access.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Infrastructure.Reporting/1.0.0": {"dependencies": {"Application": "1.0.0", "Domain": "1.0.0"}, "runtime": {"Infrastructure.Reporting.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Presentation.Wpf/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "System.Data.OleDb/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-wCEMwzzrCS6Rdv2nliZQTE+b5mwsEOivRAHrUwHd01SRqJgVxDOtHjFhGQBtMPVY5niT/bZ0bE+0kbKz4Vx2TA==", "path": "system.data.oledb/9.0.8", "hashPath": "system.data.oledb.9.0.8.nupkg.sha512"}, "Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure.Access/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure.Reporting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}