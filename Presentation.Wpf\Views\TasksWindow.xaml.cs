﻿using System.Collections.ObjectModel;
using System.Windows;
using Kufranjeh.PMS.Application;
using Kufranjeh.PMS.Domain;
using Kufranjeh.PMS.Infrastructure.Access;

namespace Presentation.Wpf.Views;

public partial class TasksWindow : Window
{
    private readonly ITaskService _taskService;
    private readonly ObservableCollection<TaskViewModel> _tasks;

    public TasksWindow()
    {
        InitializeComponent();
        
        var connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=.\\db\\Kufranjah.accdb;Persist Security Info=False;";
        var factory = new AccessConnectionFactory(connStr);
        _taskService = new TaskService(factory);
        
        _tasks = new ObservableCollection<TaskViewModel>();
        TasksDataGrid.ItemsSource = _tasks;
        
        Loaded += TasksWindow_Loaded;
    }

    private async void TasksWindow_Loaded(object sender, RoutedEventArgs e)
    {
        await LoadTasksAsync();
    }

    private async Task LoadTasksAsync()
    {
        try
        {
            _tasks.Clear();
            
            // للتبسيط، سنحمل المهام للمشروع الأول فقط
            // في التطبيق الحقيقي، يجب أن يختار المستخدم المشروع
            var tasks = await _taskService.GetTasksByProjectAsync(1);
            
            foreach (var task in tasks)
            {
                _tasks.Add(new TaskViewModel(task));
            }

            MessageBox.Show($"تم تحميل {_tasks.Count} مهمة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل المهام:\n{ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

public class TaskViewModel
{
    public TaskViewModel(TaskItem task)
    {
        TaskID = task.TaskID;
        ProjectID = task.ProjectID;
        TaskName = task.TaskName;
        AssignedTo = task.AssignedTo;
        StartDate = task.StartDate;
        EndDate = task.EndDate;
        Status = task.Status;
        ProgressPercent = task.ProgressPercent;
        Description = task.Description;
    }

    public int TaskID { get; set; }
    public int ProjectID { get; set; }
    public string TaskName { get; set; }
    public int? AssignedTo { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public Kufranjeh.PMS.Domain.TaskStatus Status { get; set; }
    public int ProgressPercent { get; set; }
    public string? Description { get; set; }

    public string StatusText => Status switch
    {
        Kufranjeh.PMS.Domain.TaskStatus.NotStarted => "لم تبدأ",
        Kufranjeh.PMS.Domain.TaskStatus.InProgress => "قيد التنفيذ",
        Kufranjeh.PMS.Domain.TaskStatus.Blocked => "معطلة",
        Kufranjeh.PMS.Domain.TaskStatus.Done => "مكتملة",
        _ => "غير محدد"
    };
}